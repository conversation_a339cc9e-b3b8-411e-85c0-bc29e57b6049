<template>
  <div>
    <v-btn
      variant="flat"
      rounded
      @click="showConfirmDialog = true"
      :loading="authStore.isLoggingOut"
      :disabled="authStore.isLoggingOut"
    >
      {{ authStore.isLoggingOut ? 'Logging out...' : 'Log out' }}
    </v-btn>

    <!-- Confirmation Dialog -->
    <v-dialog v-model="showConfirmDialog" max-width="400">
      <v-card>
        <v-card-title class="text-h6">
          Confirm Logout
        </v-card-title>
        <v-card-text>
          Are you sure you want to log out?
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey"
            variant="text"
            @click="showConfirmDialog = false"
            :disabled="authStore.isLoggingOut"
          >
            Cancel
          </v-btn>
          <v-btn
            color="primary"
            variant="text"
            @click="handleLogout"
            :loading="authStore.isLoggingOut"
            :disabled="authStore.isLoggingOut"
          >
            Log out
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Error Display -->
    <v-alert
      v-if="error"
      type="error"
      class="mt-2"
      closable
      @click:close="error = ''"
    >
      {{ error }}
    </v-alert>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from "@/store/auth";

const authStore = useAuthStore();
const error = ref<string>('');
const showConfirmDialog = ref<boolean>(false);

async function handleLogout() {
  try {
    error.value = '';
    await authStore.logout();
    showConfirmDialog.value = false;
  } catch (err) {
    console.error('Logout failed:', err);
    error.value = 'Logout failed. Please try again.';
    // Still attempt to clear local state if API call failed
    authStore.forceLogout('logout_error');
    showConfirmDialog.value = false;
  }
}
</script>
