<template>
  <div>
    <v-btn
      variant="flat"
      rounded
      @click="handleLogout"
      :loading="authStore.isLoggingOut"
      :disabled="authStore.isLoggingOut"
    >
      {{ authStore.isLoggingOut ? 'Logging out...' : 'Log out' }}
    </v-btn>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from "@/store/auth";

const authStore = useAuthStore();
const error = ref<string>('');

async function handleLogout() {
  try {
    error.value = '';
    await authStore.logout();
  } catch (err) {
    console.error('Logout failed:', err);
    error.value = 'Logout failed. Please try again.';
    // Still attempt to clear local state if API call failed
    authStore.forceLogout('logout_error');
  }
}
</script>
