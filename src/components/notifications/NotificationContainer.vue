<template>
  <v-container class="notification-container">
    <v-row>
      <v-col>
        <div class="notifications-wrapper">
          <v-alert
            v-for="notification in notificationStore.notifications"
            :key="notification.id"
            :type="notification.type"
            :title="notification.title"
            :text="notification.message"
            closable
            class="notification-item mb-2"
            @click:close="notificationStore.removeNotification(notification.id)"
          >
            <template v-if="notification.message" #text>
              {{ notification.message }}
            </template>
          </v-alert>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup lang="ts">
import { useNotificationStore } from '@/store/notifications';

const notificationStore = useNotificationStore();
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
  max-width: 400px;
}

.notifications-wrapper {
  pointer-events: auto;
}

.notification-item {
  margin-bottom: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
