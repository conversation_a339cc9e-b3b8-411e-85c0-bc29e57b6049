import { defineStore } from 'pinia';
import { ref } from 'vue';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  timeout?: number;
  persistent?: boolean;
}

export const useNotificationStore = defineStore('notifications', () => {
  const notifications = ref<Notification[]>([]);

  function addNotification(notification: Omit<Notification, 'id'>) {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const newNotification: Notification = {
      id,
      timeout: 5000, // Default 5 seconds
      ...notification
    };

    notifications.value.push(newNotification);

    // Auto-remove notification after timeout (unless persistent)
    if (!newNotification.persistent && newNotification.timeout) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.timeout);
    }

    return id;
  }

  function removeNotification(id: string) {
    const index = notifications.value.findIndex(n => n.id === id);
    if (index > -1) {
      notifications.value.splice(index, 1);
    }
  }

  function clearAll() {
    notifications.value = [];
  }

  // Convenience methods
  function success(title: string, message?: string, options?: Partial<Notification>) {
    return addNotification({ type: 'success', title, message, ...options });
  }

  function error(title: string, message?: string, options?: Partial<Notification>) {
    return addNotification({ type: 'error', title, message, ...options });
  }

  function warning(title: string, message?: string, options?: Partial<Notification>) {
    return addNotification({ type: 'warning', title, message, ...options });
  }

  function info(title: string, message?: string, options?: Partial<Notification>) {
    return addNotification({ type: 'info', title, message, ...options });
  }

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info
  };
});
