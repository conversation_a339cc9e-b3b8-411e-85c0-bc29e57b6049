// Utilities
import { defineStore } from 'pinia'
import {
  fromLocalStorage,
  randomInProduction,
} from "@/store/utils/helpers";
import router from "@/router";
import axios from 'axios';
import { getTokenExpiration, isTokenExpiringSoon } from '@/utils/jwt';

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  show: boolean;
  admin: boolean;
}

interface State {
  accessToken: string|null;
  refreshToken: string|null;
  authenticated: boolean;
  user: User|null;
  isLoading: boolean;
  tokenRenewalTimeout: NodeJS.Timeout | null;
}

export const LocalStorageNames: { [key in keyof Omit<State, 'tokenRenewalTimeout'>]: string } = {
  accessToken: randomInProduction("access_token"),
  refreshToken: randomInProduction("refresh_token"),
  authenticated: randomInProduction("authenticated"),
  user: randomInProduction("user"),
  isLoading: randomInProduction("isLoading")
}

export const useAuthStore = defineStore('auth', {
  state: (): State => ({
    accessToken: fromLocalStorage(LocalStorageNames.accessToken),
    refreshToken: fromLocalStorage(LocalStorageNames.refreshToken),
    authenticated: fromLocalStorage(LocalStorageNames.authenticated) || false,
    user: fromLocalStorage(LocalStorageNames.user),
    isLoading: false,
    tokenRenewalTimeout: null
  }),

  getters: {
    isAuthenticated: (state) => !!state.accessToken,
    isAdmin: (state) => state.user?.admin || false,
  },
  actions: {
    // Initialize auth from URL parameters (after Okta callback with PKCE)
    async initializeFromCallback() {
      const urlParams = new URLSearchParams(window.location.search)
      const sessionId = urlParams.get('session')

      if (sessionId) {
        try {
          // Exchange session ID for JWT tokens
          const response = await axios.post(`${import.meta.env.VITE_BACKEND_URL}/auth/exchange`, {
            session_id: sessionId
          })

          this.setTokens(response.data.access, response.data.refresh)

          // Clean URL but preserve return_to for router navigation
          const returnTo = urlParams.get('return_to')
          const cleanUrl = window.location.pathname + (returnTo ? `?return_to=${encodeURIComponent(returnTo)}` : '')
          window.history.replaceState({}, document.title, cleanUrl)

          return true
        } catch (error) {
          console.error('Failed to exchange session for tokens:', error)
          // Clean URL even on error
          window.history.replaceState({}, document.title, window.location.pathname)
          return false
        }
      }
      return false
    },

    // Set tokens and store them
    setTokens(access: string, refresh: string) {
      this.$patch({
        accessToken: access,
        refreshToken: refresh,
        authenticated: true
      });

      // Store tokens in localStorage
      localStorage.setItem('access_token', access);
      localStorage.setItem('refresh_token', refresh);

      // Start proactive token renewal
      this.scheduleTokenRenewal();
    },

    // Login - redirect to backend login endpoint
    login() {
      window.location.href = `${import.meta.env.VITE_BACKEND_URL}/login`
    },

    // Logout
    logout() {
      this.clear();
      router.push({name: 'Home'}).then();
    },

    // Clear all auth data
    clear() {
      this.$patch({
        authenticated: false,
        accessToken: null,
        refreshToken: null,
        user: null,
        isLoading: false,
        tokenRenewalTimeout: null
      });

      // Clear localStorage
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');

      // Clear any scheduled token renewal
      this.clearTokenRenewal();
    },

    // Refresh tokens (renamed to match specification)
    async refreshTokens() {
      if (!this.refreshToken) {
        throw new Error('No refresh token available')
      }

      try {
        const response = await axios.post<{
          access: string,
          refresh: string
        }>(`${import.meta.env.VITE_BACKEND_URL}/auth/refresh`, {
          "access": this.accessToken,
          "refresh": this.refreshToken
        });

        this.setTokens(response.data.access, response.data.refresh)
        return response.data
      } catch (error) {
        console.error('Token refresh failed:', error)
        this.logout()
        throw error
      }
    },

    // Legacy method for backward compatibility
    async refresh() {
      return this.refreshTokens();
    },

    // Fetch current user
    async fetchUser() {
      if (!this.isAuthenticated) return null

      try {
        this.isLoading = true
        const response = await axios.get<User>(`${import.meta.env.VITE_BACKEND_URL}/user`, {
          headers: {
            Authorization: `Bearer ${this.accessToken}`
          }
        })
        this.user = response.data
        return this.user
      } catch (error) {
        console.error('Failed to fetch user:', error)
        if ((error as any).response?.status === 401) {
          this.logout()
        }
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Proactive token renewal management
    scheduleTokenRenewal() {
      this.clearTokenRenewal();

      if (!this.accessToken) return;

      const expiration = getTokenExpiration(this.accessToken);
      if (!expiration) return;

      // Schedule renewal 1 minute before expiration
      const now = Date.now();
      const renewalTime = expiration - (60 * 1000); // 1 minute buffer
      const delay = renewalTime - now;

      if (delay > 0) {
        this.tokenRenewalTimeout = setTimeout(async () => {
          try {
            await this.refreshTokens();
            console.log('Token renewed proactively');
          } catch (error) {
            console.error('Proactive token renewal failed:', error);
          }
        }, delay);
      }
    },

    clearTokenRenewal() {
      if (this.tokenRenewalTimeout) {
        clearTimeout(this.tokenRenewalTimeout);
        this.tokenRenewalTimeout = null;
      }
    },

    // Check if access token needs renewal
    needsTokenRenewal(): boolean {
      if (!this.accessToken) return false;
      return isTokenExpiringSoon(this.accessToken, 1); // 1 minute buffer
    },
  }
})
