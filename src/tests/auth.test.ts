/**
 * Simple test file for authentication functionality
 * This can be run manually in the browser console or with a test runner
 */

import { useAuthStore } from '@/store/auth';
import { useNotificationStore } from '@/store/notifications';

// Test logout functionality
export async function testLogout() {
  console.log('Testing logout functionality...');
  
  const authStore = useAuthStore();
  const notificationStore = useNotificationStore();
  
  // Mock some initial state
  authStore.setTokens('mock-access-token', 'mock-refresh-token');
  
  try {
    await authStore.logout();
    console.log('✅ Logout completed successfully');
    console.log('Auth state cleared:', !authStore.isAuthenticated);
    console.log('Notifications:', notificationStore.notifications.length);
  } catch (error) {
    console.error('❌ Logout failed:', error);
  }
}

// Test force logout (user deactivation)
export function testForceLogout() {
  console.log('Testing force logout (user deactivation)...');
  
  const authStore = useAuthStore();
  const notificationStore = useNotificationStore();
  
  // Mock some initial state
  authStore.setTokens('mock-access-token', 'mock-refresh-token');
  
  authStore.forceLogout('user_deactivated');
  
  console.log('✅ Force logout completed');
  console.log('Auth state cleared:', !authStore.isAuthenticated);
  console.log('Notifications:', notificationStore.notifications.length);
}

// Test session expired
export function testSessionExpired() {
  console.log('Testing session expired...');
  
  const authStore = useAuthStore();
  const notificationStore = useNotificationStore();
  
  // Mock some initial state
  authStore.setTokens('mock-access-token', 'mock-refresh-token');
  
  authStore.forceLogout('invalid_refresh_token');
  
  console.log('✅ Session expired logout completed');
  console.log('Auth state cleared:', !authStore.isAuthenticated);
  console.log('Notifications:', notificationStore.notifications.length);
}

// Run all tests
export function runAllTests() {
  console.log('🧪 Running authentication tests...');
  
  testForceLogout();
  setTimeout(() => testSessionExpired(), 1000);
  setTimeout(() => testLogout(), 2000);
  
  console.log('✅ All tests completed');
}

// Export for browser console usage
if (typeof window !== 'undefined') {
  (window as any).authTests = {
    testLogout,
    testForceLogout,
    testSessionExpired,
    runAllTests
  };
}
