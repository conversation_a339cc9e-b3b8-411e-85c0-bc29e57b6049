<template>
  <v-layout class="rounded rounded-md">
    <v-main class="d-flex align-center justify-center" style="min-height: 300px">
      <router-view />
    </v-main>
    <!-- Global notification container -->
    <NotificationContainer />
  </v-layout>
</template>

<script setup lang="ts">
import NotificationContainer from '@/components/notifications/NotificationContainer.vue';
</script>

<style>
* {
  font-family: 'Lexend',fantasy;
}
</style>
