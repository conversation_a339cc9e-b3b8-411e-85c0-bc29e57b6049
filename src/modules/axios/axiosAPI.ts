import axios, {AxiosError, InternalAxiosRequestConfig} from "axios";
import {useAuthStore} from "@/store/auth";
import {POLLING_TIMEOUT} from "@/store/orbstars";

const axiosApi = axios.create({
  baseURL: import.meta.env.VITE_BACKEND_URL,
  timeout: 60000,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

axiosApi.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore();
    if (authStore.authenticated) {
      const token = authStore.accessToken;
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  }
)

// Helper function to create appropriate empty response for 204 status
function createEmptyResponse(originalResponse: any, url: string) {
  // Determine what type of empty data to return based on the endpoint
  let emptyData;

  if (url.includes('/orbstars/received') || url.includes('/orbstars/given')) {
    // For user orbstars endpoints, return empty pagination with value_stats
    emptyData = {
      orbstars: {
        items: [],
        count: 0,
        page: 1,
        has_next: false,
        has_prev: false
      },
      value_stats: {}
    };
  } else if (url.includes('/orbstars')) {
    // For general orbstars endpoint, return empty pagination
    emptyData = {
      items: [],
      count: 0,
      page: 1,
      has_next: false,
      has_prev: false
    };
  } else if (url.includes('/users') || url.includes('/scim/v2/Users')) {
    // For users endpoints, return empty array
    emptyData = [];
  } else if (url.includes('/values')) {
    // For values endpoint, return empty values array
    emptyData = {
      values: []
    };
  } else if (url.includes('/orbstars/leaderboard')) {
    // For leaderboard endpoint, return empty stats object
    emptyData = {};
  } else {
    // Default: return empty array for unknown list endpoints
    emptyData = [];
  }

  return {
    ...originalResponse,
    status: 200,
    data: emptyData
  };
}

// Response interceptor for token refresh and 204 handling
axiosApi.interceptors.response.use(
  (response) => {
    // Handle 204 No Content responses by converting them to 200 with empty data
    if (response.status === 204) {
      const url = response.config.url || '';
      return createEmptyResponse(response, url);
    }
    return response;
  },
  async (error: AxiosError) => {
    const authStore = useAuthStore();

    if (error.response?.status === 401) {
      const errorCode = (error.response?.data as any)?.code;

      // Handle user deactivation immediately
      if (errorCode === 'user_deactivated') {
        authStore.forceLogout('user_deactivated');
        return Promise.reject(error);
      }

      // Handle invalid refresh token
      if (errorCode === 'invalid_refresh_token') {
        authStore.forceLogout('invalid_refresh_token');
        return Promise.reject(error);
      }

      // Try token refresh for other 401 errors
      if (authStore.refreshToken) {
        try {
          await authStore.refreshTokens()
          // Retry the original request
          return axiosApi.request(error.config as InternalAxiosRequestConfig)
        } catch (refreshError) {
          // refreshTokens() method will handle logout based on error type
          return Promise.reject(refreshError)
        }
      } else {
        // No refresh token available - logout
        authStore.forceLogout('no_refresh_token');
      }
    }

    if (error.response?.status === 403) {
      authStore.logout()
    }

    return Promise.reject(error)
  }
)

export { axiosApi }
