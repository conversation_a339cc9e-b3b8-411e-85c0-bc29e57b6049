import {createRouter, createWebHistory, RouteRecordRaw} from 'vue-router';
import {useAuthStore} from "@/store/auth";

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layouts/home/<USER>'),
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('@/views/Home.vue'),
      }
    ],
  },
  {
    path: '/',
    component: () => import('@/layouts/default/Default.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          requiresAuth: true
        }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/Profile.vue'),
        meta: {
          requiresAuth: true
        }
      },
      {
        path: 'feed',
        name: 'Feed',
        component: () => import('@/views/Feed.vue'),
        meta: {
          requiresAuth: true
        }
      },
    ],
  },
  {
    path: '/idp-login',
    name: 'IDPLogin',
    beforeEnter: (to, from, next) => {
      // Extract return_to parameter and redirect to backend
      const returnTo = to.query.return_to as string
      const loginUrl = new URL('/idp-login', import.meta.env.VITE_BACKEND_URL)
      if (returnTo) {
        loginUrl.searchParams.set('return_to', returnTo)
      }
      // Redirect to backend to start PKCE flow
      window.location.href = loginUrl.href
    }
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
});

// Helper function to validate return URLs for security
function isValidReturnUrl(url: string): boolean {
  // Only allow relative URLs starting with /
  return url.startsWith('/') && !url.startsWith('//')
}

// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Handle Okta callback with PKCE session exchange
  if (to.query.session) {
    const success = await authStore.initializeFromCallback()
    if (success) {
      // Check for return_to parameter from IDP-initiated flow
      const returnTo = to.query.return_to as string
      if (returnTo && isValidReturnUrl(returnTo)) {
        next(returnTo)
      } else {
        next('/dashboard') // default redirect
      }
    } else {
      next('/')
    }
    return
  }

  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/')
    return
  }

  // Fetch user data if authenticated but no user data
  if (authStore.isAuthenticated && !authStore.user) {
    try {
      await authStore.fetchUser()
    } catch (error) {
      console.error('Failed to fetch user:', error)
      next('/')
      return
    }
  }

  next()
});

export default router;
