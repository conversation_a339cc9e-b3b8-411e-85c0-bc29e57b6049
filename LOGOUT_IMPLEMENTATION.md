# Logout and User Deactivation Implementation

This document outlines the implementation of logout functionality and user deactivation support for the Vue3 frontend application.

## Overview

The implementation includes:
- Backend logout API integration
- User deactivation detection and handling
- Enhanced error handling for authentication
- User feedback system with notifications
- Improved logout UI with confirmation dialog

## Files Modified/Created

### 1. Authentication Store (`src/store/auth.ts`)

**Changes Made:**
- Added `isLoggingOut` state for loading indicators
- Implemented proper `logout()` method that calls `POST /auth/logout`
- Added `forceLogout()` method for immediate logout scenarios
- Enhanced `refreshTokens()` method to handle user deactivation
- Added notification methods for user feedback
- Updated `clear()` method to clear user store data

**Key Features:**
- Calls backend logout endpoint with refresh token
- Handles logout failures gracefully (still clears local state)
- Detects `user_deactivated` and `invalid_refresh_token` errors
- Shows appropriate notifications for different logout scenarios

### 2. API Interceptors

**Files Updated:**
- `src/services/api.ts`
- `src/modules/axios/axiosAPI.ts`

**Changes Made:**
- Enhanced 401 error handling to detect specific error codes
- Immediate logout on `user_deactivated` errors
- Proper handling of `invalid_refresh_token` errors
- Improved retry logic for token refresh

### 3. Notification System

**New Files:**
- `src/store/notifications.ts` - Notification store with Pinia
- `src/components/notifications/NotificationContainer.vue` - Global notification display

**Features:**
- Toast-style notifications with auto-dismiss
- Support for success, error, warning, and info types
- Persistent notifications for critical messages
- Global positioning and styling

### 4. Enhanced Logout Button (`src/components/buttons/LogoutButton.vue`)

**Improvements:**
- Loading state during logout process
- Confirmation dialog before logout
- Error handling and display
- Better user experience with visual feedback

### 5. Global Integration

**Files Updated:**
- `src/App.vue` - Added global notification container

## API Integration

### Logout Endpoint
```typescript
POST /auth/logout
Content-Type: application/json
Authorization: Bearer {access_token}

Body:
{
  "refresh": "refresh_token_here"
}
```

### Error Handling

The system handles these specific error codes:
- `user_deactivated` - User account has been deactivated via SCIM
- `invalid_refresh_token` - Refresh token is invalid or expired
- `missing_auth_header` - Authorization header missing
- `invalid_token` - Access token is invalid

## User Experience

### Logout Flow
1. User clicks logout button
2. Confirmation dialog appears
3. User confirms logout
4. Loading state shown during API call
5. Success notification displayed
6. User redirected to home page
7. All authentication state cleared

### User Deactivation Flow
1. API call returns 401 with `user_deactivated` error
2. Immediate logout without API call
3. Persistent error notification shown
4. User redirected to home page
5. All authentication state cleared

### Session Expiration Flow
1. Token refresh fails with `invalid_refresh_token`
2. Automatic logout triggered
3. Warning notification shown
4. User redirected to home page

## Security Considerations

- Logout always clears local state even if API call fails
- Sensitive data is cleared from localStorage and sessionStorage
- User store data is properly cleared on logout
- Token blacklisting is handled by the backend
- No sensitive information is logged in console

## Testing

A test file has been created at `src/tests/auth.test.ts` with functions to test:
- Normal logout functionality
- Force logout (user deactivation)
- Session expiration handling

To run tests in browser console:
```javascript
// Access test functions
window.authTests.runAllTests()
```

## Configuration

### Environment Variables
- `VITE_BACKEND_URL` - Backend API base URL

### Notification Settings
- Default timeout: 5 seconds
- Persistent notifications for critical errors
- Auto-dismiss for success/info messages

## Browser Support

The implementation uses modern JavaScript features and is compatible with:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Future Enhancements

Potential improvements:
1. Add logout confirmation preferences
2. Implement "Remember me" functionality
3. Add session timeout warnings
4. Enhanced error recovery mechanisms
5. Audit logging for security events

## Troubleshooting

### Common Issues

1. **Logout API call fails**
   - Local state is still cleared
   - Check network connectivity
   - Verify backend endpoint availability

2. **Notifications not showing**
   - Ensure NotificationContainer is included in App.vue
   - Check browser console for errors
   - Verify notification store is properly initialized

3. **User deactivation not detected**
   - Verify backend returns correct error codes
   - Check API interceptor configuration
   - Ensure error response format matches expected structure

## Dependencies

- Vue 3
- Pinia (state management)
- Vuetify (UI components)
- Axios (HTTP client)

## Deployment Notes

- Ensure backend logout endpoint is properly configured
- Test user deactivation flow with SCIM integration
- Verify notification styling matches application theme
- Test logout functionality across different browsers
